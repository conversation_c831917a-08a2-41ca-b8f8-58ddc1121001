---
type: "agent_requested"
description: "Core principles for writing high-quality pytest tests for this project. Focus on performance (fast/slow markers), isolation (unit/serial), determinism (seed), and preventing lookahead bias."
---
Наша цель: Надежные, быстрые тесты. Приоритет: unit > integration > e2e.
👑 Золотые правила (TL;DR — это закон)
Скорость: Unit-тест — быстрый по умолчанию (< 1 сек).
Маркеры: @pytest.mark.slow для медленных тестов (> 2-3 сек). @pytest.mark.serial для тестов, которые нельзя распараллеливать (пишут в один файл/кэш). Маркеры обязательны.
Детерминизм: seed для random и numpy фиксируется глобально и один раз в conftest.py. Не дублируй фиксацию seed в коде тестов. Это наш стандарт.
Никаких "взглядов в будущее": Логика для момента t использует данные только до t-1. Проверяй это критически.
Именование: Имя теста должно строго следовать формату test_feature_when_condition_then_result.
Красный тест: Это сигнал к исправлению кода, а не к ослаблению теста. Никогда не увеличивай допуски (pytest.approx) и не удаляй ассерты, чтобы тест "позеленел".
🛠️ Практический чек-лист (Как делать)
Данные и изоляция:
Используй минимально необходимые фикстуры: tiny_prices_df для unit-тестов, small_prices_df для интеграционных.
Все файловые операции — только через фикстуру tmp_path. Не оставляй мусор.
Никаких time.sleep. Мокируй время.
Мокируй всё тяжелое и внешнее (I/O, сеть, сложные вычисления) через @patch.
Стиль и качество:
Один тест — один инвариант. Дублирующиеся сценарии с разными данными — объединяй через @pytest.mark.parametrize.
Никакой магии. Избегай "магических" чисел и строк. Если константа неочевидна, выноси ее в переменную с понятным именем (EXPECTED_VALUE = 5).
Понятные ассерты: Для float используй pytest.approx() или np.isclose(). Для DataFrame — pd.testing.assert_frame_equal(). Не пиши кастомные сообщения в assert, если стандартного вывода pytest достаточно для диагностики.
Полнота покрытия:
Проверяй ошибки. Тестируй не только успешное выполнение, но и ожидаемые исключения с помощью pytest.raises.
Проверяй граничные случаи. Включай в тесты сценарии с нулями, пустыми списками/DataFrame, максимальными/минимальными значениями.
Тесты оптимизации (Optuna, Numba/BLAS):
Optuna: n_trials должен быть минимальным (3-5). Используй RandomSampler и tmp_path для storage. Маркируй @pytest.mark.serial.
Numba/BLAS: При параллельном запуске тестов (pytest -n auto) ограничивай число потоков через os.environ, чтобы избежать перегрузки CPU.
🧐 Особые случаи (наши соглашения)
Приватные методы (_method): Не тестируем напрямую. Их работа проверяется через публичный API класса.
Сложные тесты: Если подготовка данных или логика теста сложны, добавь короткий комментарий #, объясняющий, что симулируется и почему.
«Плавающий» (flaky) тест: Не удаляй. Пометь маркером @pytest.mark.flaky и сообщи мне об этом. Его исправление — мой приоритет.