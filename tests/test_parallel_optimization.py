"""Тест параллельной оптимизации с потокобезопасным кэшированием."""

import sys
from pathlib import Path
import pytest
import optuna
import tempfile
import os
from unittest.mock import patch

# Добавляем корневую директорию проекта в PYTHONPATH
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.optimiser.fast_objective import FastWalkForwardObjective
from src.coint2.utils.config import load_config


@pytest.mark.slow
@pytest.mark.serial  # Параллельные тесты нельзя параллелить
class TestParallelOptimization:
    """Тесты параллельной оптимизации с потокобезопасностью."""
    
    def test_parallel_optimization_thread_safety(self):
        """
        Тест параллельной оптимизации с проверкой потокобезопасности.
        Проверяет, что кэширование работает корректно при параллельном выполнении.
        """
        try:
            config = load_config("configs/main_2024.yaml")
        except Exception as e:
            pytest.skip(f"Не удалось загрузить конфигурацию: {e}")
        
        # Ограничиваем поиск для быстроты тестирования
        search_space = {
            'rolling_window': {'type': 'int', 'low': 20, 'high': 25},
            'zscore_threshold': {'type': 'float', 'low': 2.0, 'high': 2.5}
        }
        
        # Создаем временную базу данных для Optuna
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_db:
            db_url = f"sqlite:///{tmp_db.name}"
        
        try:
            # Создаем исследование с параллельным выполнением
            study = optuna.create_study(
                direction='maximize',
                storage=db_url,
                study_name='test_thread_safety',
                load_if_exists=True
            )
            
            # Мокаем инициализацию глобального кэша
            with patch.object(FastWalkForwardObjective, '_initialize_global_rolling_cache', return_value=True):
                objective = FastWalkForwardObjective(config, search_space)
                
                # Проверяем, что блокировка инициализирована
                assert hasattr(objective, '_cache_lock'), "Должна быть блокировка _cache_lock"
                
                # Запускаем оптимизацию с 2 параллельными процессами
                # Используем небольшое количество trials для быстроты
                study.optimize(
                    objective,
                    n_trials=4,
                    n_jobs=2,  # 2 параллельных процесса
                    timeout=60  # Максимум 1 минута
                )
                
                # Проверяем, что оптимизация прошла успешно
                assert len(study.trials) > 0, "Должны быть выполнены trials"
                
                # Проверяем, что кэш содержит данные
                assert len(objective.pair_selection_cache) > 0, "Кэш должен содержать данные"
                
                print("✅ Параллельная оптимизация с потокобезопасным кэшированием работает")
                print(f"   - Выполнено trials: {len(study.trials)}")
                print(f"   - Элементов в кэше: {len(objective.pair_selection_cache)}")
                print(f"   - Лучший результат: {study.best_value:.4f}")
                
        except Exception as e:
            pytest.skip(f"Ошибка выполнения оптимизации: {e}")
        finally:
            # Удаляем временную базу данных
            try:
                os.unlink(tmp_db.name)
            except:
                pass
    
    def test_cache_lock_exists(self):
        """Простой тест наличия блокировки кэша."""
        try:
            config = load_config("configs/main_2024.yaml")
        except Exception as e:
            pytest.skip(f"Не удалось загрузить конфигурацию: {e}")
        
        search_space = {'rolling_window': {'type': 'int', 'low': 20, 'high': 25}}
        
        with patch.object(FastWalkForwardObjective, '_initialize_global_rolling_cache', return_value=True):
            try:
                objective = FastWalkForwardObjective(config, search_space)
                
                # Проверяем наличие блокировки
                assert hasattr(objective, '_cache_lock'), "Должна быть блокировка _cache_lock"
                assert hasattr(objective, 'pair_selection_cache'), "Должен быть кэш pair_selection_cache"
                
                # Проверяем, что блокировка работает
                with objective._cache_lock:
                    # Если мы дошли сюда, блокировка работает
                    pass
                
                print("✅ Блокировка кэша инициализирована и работает корректно")
                
            except Exception as e:
                pytest.skip(f"Ошибка инициализации: {e}")
    
    @pytest.mark.unit
def test_cache_key_generation(self):
        """Тест генерации ключей кэша."""
        import pandas as pd
        
        # Тестируем генерацию ключей как в реальном коде
        training_start = pd.Timestamp('2024-01-01')
        training_end = pd.Timestamp('2024-01-31')
        cache_key = f"{training_start.strftime('%Y-%m-%d')}_{training_end.strftime('%Y-%m-%d')}"
        
        expected_key = "2024-01-01_2024-01-31"
        assert cache_key == expected_key, f"Ожидали '{expected_key}', получили '{cache_key}'"
        
        # Проверяем, что одинаковые даты дают одинаковые ключи
        cache_key2 = f"{training_start.strftime('%Y-%m-%d')}_{training_end.strftime('%Y-%m-%d')}"
        assert cache_key == cache_key2, "Ключи должны быть консистентными"
        
        print("✅ Генерация ключей кэша работает корректно")


if __name__ == "__main__":
    test = TestParallelOptimization()
    test.test_cache_key_generation()
    test.test_cache_lock_exists()
    print("🎉 Базовые тесты параллельной оптимизации прошли успешно!")
    
    # Полный тест параллельной оптимизации (может занять время)
    print("\n🚀 Запуск полного теста параллельной оптимизации...")
    try:
        test.test_parallel_optimization_thread_safety()
        print("🎉 Полный тест параллельной оптимизации прошел успешно!")
    except Exception as e:
        print(f"⚠️ Полный тест пропущен: {e}")
